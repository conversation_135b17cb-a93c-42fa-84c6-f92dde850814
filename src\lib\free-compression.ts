/**
 * 免费版图片压缩服务
 * 基于Sharp.js实现本地压缩
 */

import sharp from 'sharp';
import path from 'path';
import fs from 'fs/promises';
import { CompressionService, CompressionOptions, CompressionResult } from './compression-service';

export class FreeCompressionService extends CompressionService {
  private outputDir: string;

  constructor() {
    super('free');
    this.outputDir = path.join(process.cwd(), 'public', 'compressed');
    this.ensureOutputDirectory();
  }

  private async ensureOutputDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.outputDir, { recursive: true });
    } catch (error) {
      console.error('创建输出目录失败:', error);
    }
  }

  async compressImage(
    input: Buffer | string,
    filename: string,
    options: CompressionOptions
  ): Promise<CompressionResult> {
    const startTime = Date.now();
    
    try {
      this.validateOptions(options);
      
      // 获取输入buffer
      let inputBuffer: Buffer;
      let originalSize: number;
      
      if (typeof input === 'string') {
        // 从URL下载
        const response = await fetch(input);
        if (!response.ok) {
          throw new Error(`下载失败: ${response.statusText}`);
        }
        inputBuffer = Buffer.from(await response.arrayBuffer());
      } else {
        inputBuffer = input;
      }
      
      originalSize = inputBuffer.length;
      
      // 获取原始图片信息
      const metadata = await sharp(inputBuffer).metadata();
      const isAnimated = metadata.pages && metadata.pages > 1;
      
      let result: CompressionResult;
      
      if (isAnimated && options.optimizeAnimation) {
        result = await this.compressAnimatedImage(inputBuffer, filename, options, originalSize, metadata);
      } else {
        result = await this.compressStaticImage(inputBuffer, filename, options, originalSize, metadata);
      }
      
      result.processingTime = Date.now() - startTime;
      return result;
      
    } catch (error) {
      return {
        success: false,
        originalSize: 0,
        compressedSize: 0,
        compressionRatio: 0,
        outputUrl: '',
        format: '',
        width: 0,
        height: 0,
        error: error instanceof Error ? error.message : '压缩失败',
        processingTime: Date.now() - startTime,
      };
    }
  }

  private async compressStaticImage(
    inputBuffer: Buffer,
    filename: string,
    options: CompressionOptions,
    originalSize: number,
    metadata: sharp.Metadata
  ): Promise<CompressionResult> {
    const timestamp = Date.now();
    const baseName = filename.replace(/\.[^/.]+$/, '');
    const outputFormat = options.format || 'webp';
    const outputFilename = `${timestamp}_${baseName}_compressed.${outputFormat}`;
    const outputPath = path.join(this.outputDir, outputFilename);
    
    let sharpInstance = sharp(inputBuffer);
    
    // 调整尺寸
    if (options.width || options.height) {
      const resizeOptions: sharp.ResizeOptions = {
        fit: options.preserveAspectRatio !== false ? 'inside' : 'fill',
        withoutEnlargement: true,
      };
      
      sharpInstance = sharpInstance.resize(options.width, options.height, resizeOptions);
    }
    
    // 移除元数据
    if (options.removeMetadata) {
      sharpInstance = sharpInstance.removeAlpha();
    }

    // 圆形裁剪处理
    if (options.cropToCircle) {
      const size = Math.min(options.width || 640, options.height || 640);
      sharpInstance = await this.applyCircularCrop(sharpInstance, size);
    }

    // 水冷屏幕优化
    if (options.waterCoolingOptimization) {
      sharpInstance = this.applyWaterCoolingOptimization(sharpInstance);
    }
    
    // 根据格式设置压缩参数
    switch (outputFormat) {
      case 'jpg':
        sharpInstance = sharpInstance.jpeg({
          quality: options.quality || 85,
          progressive: options.progressive !== false,
          mozjpeg: true, // 使用mozjpeg编码器获得更好的压缩
        });
        break;
        
      case 'webp':
        sharpInstance = sharpInstance.webp({
          quality: options.quality || 80,
          effort: 6, // 最高压缩努力
          smartSubsample: true,
        });
        break;
        
      case 'avif':
        sharpInstance = sharpInstance.avif({
          quality: options.quality || 75,
          effort: 9, // 最高压缩努力
        });
        break;
        
      case 'png':
        sharpInstance = sharpInstance.png({
          compressionLevel: 9,
          progressive: options.progressive !== false,
          palette: true, // 使用调色板减少文件大小
        });
        break;
        
      default:
        throw new Error(`不支持的格式: ${outputFormat}`);
    }
    
    // 保存文件
    const outputInfo = await sharpInstance.toFile(outputPath);
    
    return {
      success: true,
      originalSize,
      compressedSize: outputInfo.size,
      compressionRatio: ((originalSize - outputInfo.size) / originalSize) * 100,
      outputUrl: `/compressed/${outputFilename}`,
      format: outputFormat,
      width: outputInfo.width,
      height: outputInfo.height,
      processingTime: 0, // 将在调用方设置
    };
  }

  private async compressAnimatedImage(
    inputBuffer: Buffer,
    filename: string,
    options: CompressionOptions,
    originalSize: number,
    metadata: sharp.Metadata
  ): Promise<CompressionResult> {
    const timestamp = Date.now();
    const baseName = filename.replace(/\.[^/.]+$/, '');
    const outputFormat = options.format === 'gif' ? 'gif' : 'webp';
    const outputFilename = `${timestamp}_${baseName}_compressed.${outputFormat}`;
    const outputPath = path.join(this.outputDir, outputFilename);
    
    let sharpInstance = sharp(inputBuffer, { animated: true });
    
    // 调整尺寸
    if (options.width || options.height) {
      sharpInstance = sharpInstance.resize(options.width, options.height, {
        fit: options.preserveAspectRatio !== false ? 'inside' : 'fill',
      });
    }
    
    // 根据格式设置压缩参数
    if (outputFormat === 'webp') {
      sharpInstance = sharpInstance.webp({
        quality: options.quality || 75,
        effort: 6,
        smartSubsample: true,
      });
    } else {
      sharpInstance = sharpInstance.gif({
        effort: 10,
        colours: 256, // 限制颜色数量
      });
    }
    
    const outputInfo = await sharpInstance.toFile(outputPath);
    
    return {
      success: true,
      originalSize,
      compressedSize: outputInfo.size,
      compressionRatio: ((originalSize - outputInfo.size) / originalSize) * 100,
      outputUrl: `/compressed/${outputFilename}`,
      format: outputFormat,
      width: outputInfo.width,
      height: outputInfo.height,
      processingTime: 0,
    };
  }

  supportsFormat(format: string): boolean {
    const supportedFormats = ['jpg', 'jpeg', 'png', 'webp', 'avif', 'gif'];
    return supportedFormats.includes(format.toLowerCase());
  }

  getRecommendedOptions(
    inputFormat: string,
    fileSize: number,
    dimensions: { width: number; height: number }
  ): CompressionOptions {
    const isLarge = fileSize > 1024 * 1024; // 1MB
    const isHighRes = dimensions.width > 1920 || dimensions.height > 1080;
    
    // 基础推荐
    const options: CompressionOptions = {
      quality: isLarge ? 75 : 85,
      format: 'webp', // 默认使用WebP获得最佳压缩比
      preserveAspectRatio: true,
      removeMetadata: true,
      progressive: true,
    };
    
    // 高分辨率图片降采样
    if (isHighRes) {
      options.width = Math.min(dimensions.width, 1920);
      options.height = Math.min(dimensions.height, 1080);
    }
    
    // 动画图片优化
    if (inputFormat.toLowerCase() === 'gif') {
      options.optimizeAnimation = true;
      options.maxFrames = 50;
      options.frameRate = 15;
    }
    
    return options;
  }

  /**
   * 应用圆形裁剪
   */
  private async applyCircularCrop(sharpInstance: sharp.Sharp, size: number): Promise<sharp.Sharp> {
    try {
      // 创建圆形遮罩
      const circularMask = Buffer.from(
        `<svg width="${size}" height="${size}">
          <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="white"/>
        </svg>`
      );

      // 应用圆形遮罩
      return sharpInstance
        .resize(size, size, { fit: 'cover', position: 'center' })
        .composite([{
          input: circularMask,
          blend: 'dest-in'
        }]);
    } catch (error) {
      console.warn('圆形裁剪失败，使用方形裁剪:', error);
      return sharpInstance.resize(size, size, { fit: 'cover', position: 'center' });
    }
  }

  /**
   * 水冷屏幕优化处理
   */
  private applyWaterCoolingOptimization(sharpInstance: sharp.Sharp): sharp.Sharp {
    return sharpInstance
      .sharpen(2, 1, 2) // 增强锐度，适合小屏幕
      .modulate({
        brightness: 1.15, // 增加亮度，适合背光显示
        saturation: 1.25, // 增强饱和度，提升视觉效果
        hue: 0
      })
      .gamma(1.1); // 调整伽马值，优化对比度
  }
}
