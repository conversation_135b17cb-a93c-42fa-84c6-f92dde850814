# AIICG壁纸站 产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位
AIICG壁纸站是一个专业的AI壁纸生成与分享平台，专门为360水冷屏幕用户提供优化的壁纸解决方案。

### 1.2 目标用户
- 360水冷散热器用户
- PC DIY爱好者
- 个性化壁纸需求用户
- AI生成内容爱好者

### 1.3 核心价值
- AI生成高质量壁纸
- 专为360水冷屏幕优化
- 多种分辨率和格式支持
- 简单易用的生成界面

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 AI壁纸生成
- **输入方式**: 文本提示词描述
- **AI模型**: fal.ai (Flux Schnell)
- **生成参数**: 
  - 提示词 (必填)
  - 壁纸标题 (可选)
  - 屏幕预设选择
- **预设尺寸**:
  - 方形 480×480 (标准)
  - 方形 640×640 (高清)
  - 横屏 640×480 (宽屏)

#### 2.1.2 图片处理与优化
- **格式转换**: PNG/JPG/WebP
- **尺寸调整**: 自动适配目标分辨率
- **360水冷优化**:
  - 增强锐度适合小屏显示
  - 调整亮度和饱和度
  - 圆形/方形屏幕适配
- **缩略图生成**: 300×200 WebP格式

#### 2.1.3 壁纸管理
- **存储方式**: 本地文件系统 + JSON数据
- **元数据管理**: 标题、提示词、标签、下载次数
- **分类展示**: 全部壁纸、热门壁纸
- **搜索功能**: 按标题、提示词、标签搜索

#### 2.1.4 下载功能
- **一键下载**: 点击下载按钮直接保存
- **下载统计**: 记录下载次数
- **文件命名**: 自动使用壁纸标题命名

### 2.2 界面功能

#### 2.2.1 首页
- Hero区域展示产品特色
- AI生成器表单
- 精选壁纸展示

#### 2.2.2 导航栏
- Logo和品牌名称
- 搜索框
- 导航菜单

#### 2.2.3 壁纸画廊
- 瀑布流布局
- 壁纸卡片信息展示
- 过滤和排序功能

## 3. 技术架构

### 3.1 技术栈
- **前端**: Next.js 14 + TypeScript + TailwindCSS
- **AI生图**: fal.ai API (Flux Schnell模型)
- **图片处理**: Sharp.js
- **数据存储**: JSON文件 + 本地文件系统
- **部署**: Vercel

### 3.2 API设计
- `POST /api/generate` - 生成壁纸
- `GET /api/wallpapers` - 获取壁纸列表
- `POST /api/download/[id]` - 下载壁纸

### 3.3 数据结构
```typescript
interface Wallpaper {
  id: string;
  title: string;
  prompt: string;
  imageUrl: string;
  thumbnailUrl: string;
  width: number;
  height: number;
  format: 'png' | 'jpg' | 'webp';
  createdAt: string;
  downloads: number;
  tags: string[];
  optimizedFor360: boolean;
}
```

## 4. 用户体验

### 4.1 用户流程
1. 访问网站首页
2. 输入壁纸描述和标题
3. 选择360水冷屏幕预设
4. 点击生成按钮
5. 等待AI生成结果
6. 预览生成的壁纸
7. 浏览壁纸画廊
8. 下载喜欢的壁纸

### 4.2 响应式设计
- 支持桌面端、平板、手机
- 自适应布局和组件
- 触摸友好的交互

### 4.3 性能优化
- 图片懒加载
- WebP格式优化
- 缩略图预览
- 缓存策略

## 5. 商业模式

### 5.1 MVP阶段 (当前)
- 免费使用
- 基础功能完整
- 用户反馈收集

### 5.2 未来扩展
- 高级生图参数
- 批量生成功能
- 用户账户系统
- 付费高清下载
- 社区分享功能

## 6. 风险评估

### 6.1 技术风险
- **AI API稳定性**: fal.ai服务可用性
- **图片处理性能**: 大量并发处理
- **存储空间**: 本地存储容量限制

### 6.2 缓解措施
- API错误处理和重试机制
- 图片压缩和格式优化
- 定期清理和备份策略

## 7. 成功指标

### 7.1 用户指标
- 日活跃用户数
- 壁纸生成成功率
- 用户留存率

### 7.2 产品指标
- 壁纸下载量
- 生成响应时间
- 用户满意度

## 8. 开发计划

### 8.1 已完成 ✅
- 项目初始化和环境配置
- AI生图API集成
- 图片处理和优化
- 基础UI组件开发
- 数据存储方案

### 8.2 待优化
- 错误处理完善
- 性能优化
- 用户体验细节
- 部署和域名配置

---

**版本**: v1.0  
**更新日期**: 2024-12-28  
**状态**: MVP开发完成
