<!DOCTYPE html>
<html>
<head>
    <title>图片测试</title>
</head>
<body>
    <h1>图片显示测试</h1>
    
    <h2>本地压缩图片测试</h2>
    <img src="/compressed/1751085946484_01_compressed.webp" alt="测试图片" style="max-width: 300px; border: 1px solid red;">
    
    <h2>Cloudinary图片测试</h2>
    <img src="https://res.cloudinary.com/dig04lnn7/image/upload/c_fit,w_640,h_640/q_80/f_jpg/compressed/1751086000000_test.jpg" alt="Cloudinary测试" style="max-width: 300px; border: 1px solid blue;">
    
    <script>
        // 监听图片加载事件
        document.querySelectorAll('img').forEach((img, index) => {
            img.onload = () => console.log(`图片 ${index + 1} 加载成功`);
            img.onerror = () => console.log(`图片 ${index + 1} 加载失败`);
        });
    </script>
</body>
</html>
